import '../models/weight_entry.dart';

double calculateAverageLoss(List<WeightEntry> entries) {
  if (entries.length < 2) return 0.0;

  // Sort entries by date just in case they are not already sorted
  entries.sort((a, b) => a.date.compareTo(b.date));

  final firstEntry = entries.first;
  final lastEntry = entries.last;

  final double totalLoss = firstEntry.weight - lastEntry.weight;
  final int daysElapsed = lastEntry.date.difference(firstEntry.date).inDays;

  if (daysElapsed <= 0) {
    return 0.0;
  }

  return totalLoss / daysElapsed;
}
