import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/weight_entry.dart';
import '../widgets/weight_chart.dart';
import '../utils/calculations.dart';
import 'package:intl/intl.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController _weightController = TextEditingController();
  List<WeightEntry> _entries = [];
  double? _dailyLoss;
  bool _isEditingWeight = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _loadEntries();
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        setState(() {
          _isEditingWeight = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _weightController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  Future<void> _loadEntries() async {
    final prefs = await SharedPreferences.getInstance();
    final data = prefs.getString('weight_entries');
    if (data != null) {
      final List<dynamic> jsonList = jsonDecode(data);
      setState(() {
        _entries = jsonList.map((e) => WeightEntry.fromJson(e)).toList();
      });
    }
  }

  Future<void> _saveEntries() async {
    final prefs = await SharedPreferences.getInstance();
    final jsonList = _entries.map((e) => e.toJson()).toList();
    await prefs.setString('weight_entries', jsonEncode(jsonList));
  }

  void _addWeight() {
    if (_weightController.text.isNotEmpty) {
      setState(() {
        double currentWeight = double.parse(_weightController.text);
        _entries.add(WeightEntry(date: DateTime.now(), weight: currentWeight));
        if (_entries.length > 1) {
          _dailyLoss = _entries[_entries.length - 2].weight - currentWeight;
        }
        _weightController.clear();
        _isEditingWeight = false;
        _focusNode.unfocus();
      });
      _saveEntries();
    } else {
      setState(() {
        _isEditingWeight = false;
        _focusNode.unfocus();
      });
    }
  }

  void _deleteEntry(WeightEntry entry) {
    setState(() {
      _entries.remove(entry);
    });
    _saveEntries();
  }

  double get _latestWeight => _entries.isNotEmpty ? _entries.last.weight : 0.0;

  double _expectedWeight(int days) {
    return _latestWeight - calculateAverageLoss(_entries) * days;
  }

  void _addDummyDataForMonth() {
    final now = DateTime.now();
    final startWeight = 80.0; // Starting weight
    final random = Random();

    setState(() {
      _entries.clear(); // Clear existing entries

      // Generate 30 days of dummy data
      for (int i = 29; i >= 0; i--) {
        final date = now.subtract(Duration(days: i));
        // Simulate gradual weight loss with some daily fluctuation
        final baseWeight = startWeight - (29 - i) * 0.1; // Lose ~0.1kg per day on average
        final fluctuation = (random.nextDouble() - 0.5) * 0.4; // ±0.2kg daily fluctuation
        final weight = baseWeight + fluctuation;

        _entries.add(WeightEntry(
          date: date,
          weight: double.parse(weight.toStringAsFixed(1)),
        ));
      }

      // Calculate daily loss for the last entry
      if (_entries.length > 1) {
        _dailyLoss = _entries[_entries.length - 2].weight - _entries.last.weight;
      }
    });

    _saveEntries();
  }

  @override
  Widget build(BuildContext context) {
    final avgLoss = calculateAverageLoss(_entries);
    final weekExpected = _expectedWeight(7);
    final monthExpected = _expectedWeight(30);
    final threeMonthExpected = _expectedWeight(90);
    final sixMonthExpected = _expectedWeight(180);
    final yearExpected = _expectedWeight(365);
    final latestWeight = _entries.isNotEmpty ? _entries.last.weight : 0.0;
    final latestDate =
        _entries.isNotEmpty ? _entries.last.date : DateTime.now();
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Container(
                  height: 44,
                  color: Color(0xFFFFCC33).withOpacity(0.8),
                  padding: EdgeInsets.symmetric(horizontal: 8),
                  child: Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _isEditingWeight = true;
                              _weightController.text = '';
                              _focusNode.requestFocus();
                            });
                          },
                          child: _isEditingWeight
                              ? Padding(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0),
                                  child: TextField(
                                    controller: _weightController,
                                    focusNode: _focusNode,
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true),
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: Color(0xFF09602D),
                                    ),
                                    decoration: InputDecoration(
                                      isDense: true,
                                      contentPadding: EdgeInsets.zero,
                                      border: InputBorder.none,
                                    ),
                                    onSubmitted: (_) => _addWeight(),
                                  ),
                                )
                              : Container(
                                  alignment: Alignment.centerLeft,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12.0),
                                  child: Text(
                                    latestWeight > 0
                                        ? latestWeight.toStringAsFixed(1)
                                        : '--',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                      color: Color(0xFF09602D),
                                    ),
                                  ),
                                ),
                        ),
                      ),
                      InkWell(
                        onTap: () async {
                          DateTime? picked = await showDatePicker(
                            context: context,
                            initialDate: latestDate,
                            firstDate: DateTime(2000),
                            lastDate: DateTime(2100),
                          );
                          if (picked != null) {
                            double? weight = await showDialog<double>(
                              context: context,
                              builder: (context) {
                                final controller = TextEditingController();
                                return AlertDialog(
                                  title: Text(
                                      'Enter weight for ${DateFormat('EEE d/M').format(picked)}'),
                                  content: TextField(
                                    controller: controller,
                                    keyboardType: TextInputType.number,
                                    decoration: InputDecoration(
                                        labelText: 'Weight (kg)'),
                                  ),
                                  actions: [
                                    TextButton(
                                      onPressed: () => Navigator.pop(context),
                                      child: Text('Cancel'),
                                    ),
                                    ElevatedButton(
                                      onPressed: () {
                                        final val =
                                            double.tryParse(controller.text);
                                        if (val != null)
                                          Navigator.pop(context, val);
                                      },
                                      child: Text('OK'),
                                    ),
                                  ],
                                );
                              },
                            );
                            if (weight != null) {
                              setState(() {
                                final idx = _entries.indexWhere((e) =>
                                    e.date.year == picked.year &&
                                    e.date.month == picked.month &&
                                    e.date.day == picked.day);
                                if (idx != -1) {
                                  _entries[idx] =
                                      WeightEntry(date: picked, weight: weight);
                                } else {
                                  _entries.add(WeightEntry(
                                      date: picked, weight: weight));
                                  _entries
                                      .sort((a, b) => a.date.compareTo(b.date));
                                }
                              });
                              _saveEntries();
                            }
                          }
                        },
                        child: Container(
                          margin: EdgeInsets.symmetric(horizontal: 8),
                          padding:
                              EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                          decoration: BoxDecoration(
                            color: Colors.yellow.shade700,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.calendar_today,
                                  color: Color(0xFF09602D), size: 20),
                              SizedBox(width: 4),
                              Text('CL',
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xFF09602D))),
                            ],
                          ),
                        ),
                      ),
                      TextButton(
                        style: TextButton.styleFrom(
                          foregroundColor: Color(0xFF09602D),
                          padding: EdgeInsets.symmetric(horizontal: 16),
                          textStyle: TextStyle(
                              fontWeight: FontWeight.bold, fontSize: 16),
                        ),
                        onPressed: _addWeight,
                        child: Text('ADD'),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 16),
                if (_dailyLoss != null)
                  Text('Daily Loss: ${_dailyLoss!.toStringAsFixed(2)} kg'),
                SizedBox(height: 16),
                // Button to generate dummy data for testing
                if (_entries.isEmpty)
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFF09602D),
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      textStyle: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14
                      ),
                    ),
                    onPressed: _addDummyDataForMonth,
                    child: Text('ADD DUMMY DATA (1 MONTH)'),
                  ),
                SizedBox(height: 16),
                if (_entries.isNotEmpty)
                  WeightChart(
                    entries: _entries,
                    averageLoss: avgLoss,
                    onDelete: _deleteEntry,
                    expectedWeights: {
                      'week': weekExpected,
                      'month': monthExpected,
                      '3m': threeMonthExpected,
                      '6m': sixMonthExpected,
                      '1y': yearExpected,
                    },
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
