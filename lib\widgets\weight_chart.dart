import 'dart:ui';
import 'dart:math';
import 'package:flutter/material.dart';
// import 'package:fl_chart/fl_chart.dart'; // No longer needed
import 'package:syncfusion_flutter_charts/charts.dart';
import 'package:intl/intl.dart';
import '../models/weight_entry.dart';

class WeightChart extends StatefulWidget {
  final List<WeightEntry> entries;
  final double averageLoss;
  final void Function(WeightEntry entry)? onDelete;
  final Map<String, double>? expectedWeights;

  WeightChart(
      {required this.entries,
      required this.averageLoss,
      this.onDelete,
      this.expectedWeights});

  @override
  _WeightChartState createState() => _WeightChartState();
}

class _WeightChartState extends State<WeightChart>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  String _currentView = 'Week';
  final List<String> _views = ['Week', 'Month', '3m', '6m', '1y'];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _views.length, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _currentView = _views[_tabController.index];
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  List<WeightEntry?> _getWeekEntries() {
    // Get current week entries (Sunday to Saturday)
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final sundayOfWeek = today.subtract(Duration(days: now.weekday % 7)); // Sunday = 0
    List<WeightEntry?> weekEntries = List.filled(7, null);

    for (var entry in widget.entries) {
      final entryDate = DateTime(entry.date.year, entry.date.month, entry.date.day);
      int diff = entryDate.difference(sundayOfWeek).inDays;
      if (diff >= 0 && diff < 7) {
        weekEntries[diff] = entry;
      }
    }
    return weekEntries;
  }

  List<WeightEntry?> _getMonthEntries() {
    // Get current month entries
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final daysInMonth = DateTime(now.year, now.month + 1, 0).day;
    List<WeightEntry?> monthEntries = List.filled(daysInMonth, null);

    for (var entry in widget.entries) {
      final entryDate = DateTime(entry.date.year, entry.date.month, entry.date.day);
      if (entryDate.year == now.year && entryDate.month == now.month) {
        int dayIndex = entryDate.day - 1; // Day 1 = index 0
        if (dayIndex >= 0 && dayIndex < daysInMonth) {
          monthEntries[dayIndex] = entry;
        }
      }
    }
    return monthEntries;
  }

  List<WeightEntry?> _getPeriodEntries(int days) {
    // Get entries for the current period (last X days from today)
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final startDate = today.subtract(Duration(days: days - 1));

    List<WeightEntry?> periodEntries = List.filled(days, null);
    for (var entry in widget.entries) {
      final entryDate = DateTime(entry.date.year, entry.date.month, entry.date.day);
      int diff = entryDate.difference(startDate).inDays;
      if (diff >= 0 && diff < days) {
        periodEntries[diff] = entry;
      }
    }
    return periodEntries;
  }

  List<WeightEntry?> _getFilteredEntries() {
    switch (_currentView) {
      case 'Week':
        return _getWeekEntries();
      case 'Month':
        return _getMonthEntries();
      case '3m':
        return _getPeriodEntries(90);
      case '6m':
        return _getPeriodEntries(180);
      case '1y':
        return _getPeriodEntries(365);
      default:
        return _getWeekEntries();
    }
  }

  Widget _buildSyncfusionBarChart(
      List<WeightEntry?> entries, List<String> dayNames, double avgLoss) {
    // Build a robust mapping: for each day of the week (Sunday to Saturday), find the entry for that day
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday % 7));
    List<WeightEntry?> weekEntries = List.filled(7, null);
    for (var entry in entries) {
      if (entry == null) continue;
      final entryDate =
          DateTime(entry.date.year, entry.date.month, entry.date.day);
      int diff = entryDate.difference(startOfWeek).inDays;
      if (diff >= 0 && diff < 7) {
        weekEntries[diff] = entry;
      }
    }
    // Always show 7 columns for the week, one for each day (S, M, T, W, T, F, S)
    List<_RangeBarData> chartData = [];
    double? previousWeight;
    for (int i = 0; i < 7; i++) {
      final entry = weekEntries[i];
      double? open = previousWeight;
      double? close = entry?.weight;
      double? diff = (open != null && close != null) ? close - open : null;
      chartData.add(_RangeBarData(
        day: dayNames[i],
        open: open,
        close: close,
        diff: diff,
      ));
      if (entry != null) previousWeight = entry.weight;
    }
    // Calculate min and max for Y axis, with null checks
    final weights = chartData
        .where((d) => d.open != null && d.close != null)
        .expand((d) => [d.open!, d.close!])
        .toList();
    double minY =
        weights.isNotEmpty ? weights.reduce((a, b) => a < b ? a : b) - 1 : 0;
    double maxY =
        weights.isNotEmpty ? weights.reduce((a, b) => a > b ? a : b) + 1 : 1;
    final fullDayNames = [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday',
      'Saturday',
      'Sunday'
    ];
    return LayoutBuilder(
      builder: (context, constraints) {
        final chartWidth = constraints.maxWidth;
        final columnWidth = chartWidth / 7;
        final circleRadius = 18.0;
        return SizedBox(
          height: 280, // enough for chart + labels
          width: double.infinity,
          child: Stack(
            children: [
              // Chart
              Positioned(
                left: 0,
                right: 0,
                top: 0,
                height: 240,
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xFFFFCC33),
                    borderRadius: BorderRadius.circular(2),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black12,
                        blurRadius: 8,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: SfCartesianChart(
                    margin: EdgeInsets.zero,
                    plotAreaBorderWidth: 0,
                    primaryXAxis: CategoryAxis(
                      majorGridLines: MajorGridLines(width: 0),
                      axisLine: AxisLine(width: 0),
                      labelStyle: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF09602D),
                        fontSize: 14,
                      ),
                      isVisible: false, // Hide X-axis labels
                    ),
                    primaryYAxis: CategoryAxis(
                      // Show full day names as Y axis
                      isInversed: true,
                      labelPlacement: LabelPlacement.onTicks,
                      majorGridLines:
                          MajorGridLines(width: 0.5, color: Colors.white),
                      axisLine: AxisLine(width: 0),
                      labelStyle: TextStyle(
                        color: Color(0xFF09602D),
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      interval: 1,
                    ),
                    series: <RangeColumnSeries<_RangeBarData, String>>[
                      RangeColumnSeries<_RangeBarData, String>(
                        dataSource: chartData,
                        xValueMapper: (_RangeBarData data, _) => data.day,
                        lowValueMapper: (_RangeBarData data, _) => data.open,
                        highValueMapper: (_RangeBarData data, _) => data.close,
                        pointColorMapper: (_RangeBarData data, _) {
                          if (data.diff == null) return Color(0xFF09602D);
                          if (data.diff! < 0)
                            return Color(0xFF09602D); // Green for loss
                          if (data.diff! > 0)
                            return Color(0xFFD32F2F); // Red for gain
                          return Colors.grey;
                        },
                        borderRadius: BorderRadius.circular(4),
                        width: 0.4, // Make bars thinner for better distribution
                        spacing: 0.2, // Add spacing between bars
                        dataLabelSettings: DataLabelSettings(
                          isVisible: true,
                          labelAlignment: ChartDataLabelAlignment.top,
                          textStyle: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                          builder: (dynamic data, dynamic point, dynamic series,
                              int pointIndex, int seriesIndex) {
                            if (data.diff == null) return Text('');
                            String sign = data.diff! > 0 ? '+' : '';
                            return Text(
                              '${sign}${data.diff!.toStringAsFixed(1)}',
                              style: TextStyle(
                                color: data.diff! < 0
                                    ? Color(0xFF09602D)
                                    : Color(0xFFD32F2F),
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Overlay circles for day names at the bottom, perfectly aligned
              for (int i = 0; i < 7; i++)
                Positioned(
                  left: (i + 0.5) * columnWidth - circleRadius,
                  bottom: 0,
                  child: CircleAvatar(
                    backgroundColor: Color(0xFFFFCC33),
                    radius: circleRadius,
                    child: Text(
                      dayNames[i],
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF09602D),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildWeightTable(List<WeightEntry?> entries) {
    // Only show actual entries (not nulls)
    final actualEntries =
        entries.where((e) => e != null).cast<WeightEntry>().toList();
    return Container(
      margin: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Daily Weight Entries',
            style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
          ),
          SizedBox(height: 8),
          Row(
            children: [
              Expanded(flex: 2, child: Text('Date')),
              Expanded(flex: 2, child: Text('Weight')),
              Expanded(flex: 2, child: Text('Diff')),
              Expanded(flex: 2, child: Text('Expected')),
              SizedBox(width: 32),
            ],
          ),
          ...actualEntries.asMap().entries.map((entry) {
            final index = entry.key;
            final weightEntry = entry.value;
            final previousWeight =
                index > 0 ? actualEntries[index - 1].weight : null;
            final difference = previousWeight != null
                ? weightEntry.weight - previousWeight
                : 0.0;
            final isLoss = difference < 0;
            // Calculate expected weight for this day
            final avgLoss = widget.averageLoss;
            final daysSinceFirst =
                weightEntry.date.difference(actualEntries.first.date).inDays;
            final expectedWeight =
                actualEntries.first.weight - avgLoss * daysSinceFirst;
            return Container(
              padding: EdgeInsets.symmetric(vertical: 8, horizontal: 8),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${_getDayName(weightEntry.date)} ${weightEntry.date.day}/${weightEntry.date.month}',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${weightEntry.weight.toStringAsFixed(1)} kg',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      previousWeight != null
                          ? '${difference.toStringAsFixed(1)} kg'
                          : '-',
                      style: TextStyle(
                        color: isLoss ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Text(
                      '${expectedWeight.toStringAsFixed(1)} kg',
                      style: TextStyle(
                          color: Colors.blueGrey, fontWeight: FontWeight.w500),
                    ),
                  ),
                  IconButton(
                    icon: Icon(Icons.delete, color: Colors.red),
                    onPressed: () {
                      if (widget.onDelete != null)
                        widget.onDelete!(weightEntry);
                    },
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  String _getDayName(DateTime date) {
    // weekday: 1=Monday, 7=Sunday
    // We want: 0=Sunday, 1=Monday, ..., 6=Saturday
    final dayIndex = date.weekday == 7 ? 0 : date.weekday;
    return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][dayIndex];
  }

  Widget _buildChart(List<WeightEntry?> entries, double avgLoss) {
    switch (_currentView) {
      case 'Week':
        return _buildWeekChart(entries, avgLoss);
      case 'Month':
        return _buildMonthChart(entries, avgLoss);
      case '3m':
      case '6m':
      case '1y':
        return _buildPeriodChart(entries, avgLoss);
      default:
        return _buildWeekChart(entries, avgLoss);
    }
  }

  Widget _buildWeekChart(List<WeightEntry?> entries, double avgLoss) {
    return _buildWeekSpecificChart(entries, avgLoss);
  }

  Widget _buildMonthChart(List<WeightEntry?> entries, double avgLoss) {
    return _buildCombinedChart(entries, avgLoss, 'month');
  }

  Widget _buildWeekSpecificChart(List<WeightEntry?> entries, double avgLoss) {
    final actualEntries = entries.where((e) => e != null).cast<WeightEntry>().toList();

    // Sort entries by date
    actualEntries.sort((a, b) => a.date.compareTo(b.date));

    // Create complete week structure with all 7 days (Sunday to Saturday)
    final now = DateTime.now();
    final sundayOfWeek = now.subtract(Duration(days: now.weekday % 7));
    List<_RangeBarData> rangeData = [];

    final dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    WeightEntry? previousEntry;

    // Create data for all 7 days, even if no weight entry exists
    for (int i = 0; i < 7; i++) {
      final currentDay = sundayOfWeek.add(Duration(days: i));
      final dayEntry = actualEntries.where((e) =>
        e.date.year == currentDay.year &&
        e.date.month == currentDay.month &&
        e.date.day == currentDay.day
      ).firstOrNull;

      if (dayEntry != null && previousEntry != null) {
        // Create range bar from previous weight to current weight
        final weightDiff = dayEntry.weight - previousEntry.weight;
        final isIncrease = weightDiff > 0;
        final diffText = "${isIncrease ? '+' : ''}${weightDiff.toStringAsFixed(1)}";

        rangeData.add(_RangeBarData(
          day: dayNames[i],
          open: previousEntry.weight,  // Previous weight (bottom of bar)
          close: dayEntry.weight,      // Current weight (top of bar)
          diff: isIncrease ? 1.0 : -1.0, // Use for color mapping
          diffText: diffText, // Display text like "+0.2" or "-0.2"
        ));
      } else {
        // Add placeholder for days without data to maintain spacing
        rangeData.add(_RangeBarData(
          day: dayNames[i],
          open: null,
          close: null,
          diff: null,
          diffText: null,
        ));
      }

      // Update previous entry if current day has data
      if (dayEntry != null) {
        previousEntry = dayEntry;
      }
    }

    // Calculate expected weights
    List<WeightEntry> expectedEntries = [];
    if (actualEntries.isNotEmpty) {
      final firstEntry = actualEntries.first;
      for (int i = 0; i < 7; i++) {
        final date = sundayOfWeek.add(Duration(days: i));
        final daysDiff = date.difference(firstEntry.date).inDays;
        final expectedWeight = firstEntry.weight + (avgLoss * daysDiff);
        expectedEntries.add(WeightEntry(date: date, weight: expectedWeight));
      }
    }

    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: Color(0xFFFFCC33),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SfCartesianChart(
        primaryXAxis: CategoryAxis(
          majorGridLines: MajorGridLines(width: 0),
          labelStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          axisLine: AxisLine(width: 0),
          majorTickLines: MajorTickLines(width: 0),
          plotOffset: 10,
        ),
        primaryYAxis: NumericAxis(
          majorGridLines: MajorGridLines(width: 0),
          minorGridLines: MinorGridLines(width: 0),
          labelStyle: TextStyle(fontSize: 12),
          title: AxisTitle(text: 'Weight (kg)', textStyle: TextStyle(fontSize: 12)),
          axisLine: AxisLine(width: 0),
          majorTickLines: MajorTickLines(width: 0),
          plotOffset: 10,
        ),
        plotAreaBorderWidth: 0,
        series: <CartesianSeries>[
          // Range bars (from previous weight to current weight)
          RangeColumnSeries<_RangeBarData, String>(
            dataSource: rangeData.where((data) => data.open != null && data.close != null).toList(),
            xValueMapper: (_RangeBarData data, _) => data.day,
            lowValueMapper: (_RangeBarData data, _) => data.open,   // Previous weight (bottom)
            highValueMapper: (_RangeBarData data, _) => data.close, // Current weight (top)
            pointColorMapper: (_RangeBarData data, _) => data.diff! > 0 ? Colors.red : Colors.green,
            borderWidth: 2,
            borderColor: Colors.black,
            dataLabelSettings: DataLabelSettings(
              isVisible: true,
              textStyle: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              labelAlignment: ChartDataLabelAlignment.top,
              builder: (dynamic data, dynamic point, dynamic series, int pointIndex, int seriesIndex) {
                final rangeData = data as _RangeBarData;
                return Container(
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    rangeData.diffText ?? '',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: rangeData.diff! > 0 ? Colors.red : Colors.green,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCombinedChart(List<WeightEntry?> entries, double avgLoss, String period) {
    final actualEntries = entries.where((e) => e != null).cast<WeightEntry>().toList();
    if (actualEntries.isEmpty) {
      return Container(
        height: 280,
        decoration: BoxDecoration(
          color: Color(0xFFFFCC33),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(child: Text('No data available for this $period')),
      );
    }

    // Sort entries by date
    actualEntries.sort((a, b) => a.date.compareTo(b.date));

    // Calculate range bar data (previous weight to current weight)
    List<_RangeBarData> rangeData = [];
    for (int i = 0; i < actualEntries.length; i++) {
      if (i > 0) {
        // Create range bar from previous weight to current weight
        final previousWeight = actualEntries[i - 1].weight;
        final currentWeight = actualEntries[i].weight;
        final weightDiff = currentWeight - previousWeight;
        final isIncrease = weightDiff > 0;
        final diffText = "${isIncrease ? '+' : ''}${weightDiff.toStringAsFixed(1)}";

        rangeData.add(_RangeBarData(
          day: period == 'week' ? _getDayName(actualEntries[i].date) : '${actualEntries[i].date.day}',
          open: previousWeight,  // Previous weight (bottom of bar)
          close: currentWeight,  // Current weight (top of bar)
          diff: isIncrease ? 1.0 : -1.0, // Use for color mapping
          diffText: diffText, // Display text like "+0.2" or "-0.2"
        ));
      }
    }

    // Calculate expected weights for red dotted line
    List<WeightEntry> expectedEntries = [];
    if (actualEntries.isNotEmpty) {
      final firstEntry = actualEntries.first;
      final lastEntry = actualEntries.last;

      for (DateTime date = firstEntry.date; date.isBefore(lastEntry.date.add(Duration(days: 1))); date = date.add(Duration(days: 1))) {
        final daysDiff = date.difference(firstEntry.date).inDays;
        final expectedWeight = firstEntry.weight + (avgLoss * daysDiff);
        expectedEntries.add(WeightEntry(date: date, weight: expectedWeight));
      }
    }

    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: Color(0xFFFFCC33),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SfCartesianChart(
        primaryXAxis: CategoryAxis(
          majorGridLines: MajorGridLines(width: 0),
          labelStyle: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
          axisLine: AxisLine(width: 0),
          majorTickLines: MajorTickLines(width: 0),
          plotOffset: 10,
        ),
        primaryYAxis: NumericAxis(
          majorGridLines: MajorGridLines(width: 0),
          minorGridLines: MinorGridLines(width: 0),
          labelStyle: TextStyle(fontSize: 12),
          title: AxisTitle(text: 'Weight (kg)', textStyle: TextStyle(fontSize: 12)),
          axisLine: AxisLine(width: 0),
          majorTickLines: MajorTickLines(width: 0),
          plotOffset: 10,
        ),
        plotAreaBorderWidth: 0,
        series: <CartesianSeries>[
          // Range bars (from previous weight to current weight)
          RangeColumnSeries<_RangeBarData, String>(
            dataSource: rangeData,
            xValueMapper: (_RangeBarData data, _) => data.day,
            lowValueMapper: (_RangeBarData data, _) => data.open,   // Previous weight (bottom)
            highValueMapper: (_RangeBarData data, _) => data.close, // Current weight (top)
            pointColorMapper: (_RangeBarData data, _) => data.diff! > 0 ? Colors.red : Colors.green,
            borderWidth: 2,
            borderColor: Colors.black,
            dataLabelSettings: DataLabelSettings(
              isVisible: true,
              textStyle: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: Colors.black,
              ),
              labelAlignment: ChartDataLabelAlignment.top,
              builder: (dynamic data, dynamic point, dynamic series, int pointIndex, int seriesIndex) {
                final rangeData = data as _RangeBarData;
                return Container(
                  padding: EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.9),
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: Text(
                    rangeData.diffText ?? '',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: rangeData.diff! > 0 ? Colors.red : Colors.green,
                    ),
                  ),
                );
              },
            ),
          ),
          // Blue connecting line for actual weights
          LineSeries<WeightEntry, DateTime>(
            dataSource: actualEntries,
            xValueMapper: (WeightEntry entry, _) => entry.date,
            yValueMapper: (WeightEntry entry, _) => entry.weight,
            color: Colors.blue,
            width: 2,
            markerSettings: MarkerSettings(
              isVisible: true,
              color: Colors.blue,
              borderColor: Colors.white,
              borderWidth: 1,
              height: 6,
              width: 6,
            ),
          ),
          // Red dotted line for expected weights
          LineSeries<WeightEntry, DateTime>(
            dataSource: expectedEntries,
            xValueMapper: (WeightEntry entry, _) => entry.date,
            yValueMapper: (WeightEntry entry, _) => entry.weight,
            color: Colors.red,
            width: 2,
            dashArray: <double>[5, 5],
            markerSettings: MarkerSettings(isVisible: false),
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodChart(List<WeightEntry?> entries, double avgLoss) {
    final actualEntries = entries.where((e) => e != null).cast<WeightEntry>().toList();
    if (actualEntries.isEmpty) {
      return Container(
        height: 280,
        decoration: BoxDecoration(
          color: Color(0xFFFFCC33),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Center(child: Text('No data available for this period')),
      );
    }

    // Calculate expected weights for the entire period
    List<WeightEntry> expectedEntries = [];
    if (actualEntries.isNotEmpty) {
      final firstEntry = actualEntries.first;
      final lastEntry = actualEntries.last;
      final startDate = firstEntry.date;
      final endDate = lastEntry.date;

      for (DateTime date = startDate; date.isBefore(endDate.add(Duration(days: 1))); date = date.add(Duration(days: 1))) {
        final daysDiff = date.difference(firstEntry.date).inDays;
        final expectedWeight = firstEntry.weight + (avgLoss * daysDiff);
        expectedEntries.add(WeightEntry(date: date, weight: expectedWeight));
      }
    }

    return Container(
      height: 280,
      decoration: BoxDecoration(
        color: Color(0xFFFFCC33),
        borderRadius: BorderRadius.circular(8),
      ),
      child: SfCartesianChart(
        primaryXAxis: DateTimeAxis(
          dateFormat: _currentView == '3m' ? DateFormat('dd/MM') :
                     _currentView == '6m' ? DateFormat('MMM') : DateFormat('MMM yy'),
          intervalType: _currentView == '3m' ? DateTimeIntervalType.days :
                      _currentView == '6m' ? DateTimeIntervalType.months : DateTimeIntervalType.months,
          majorGridLines: MajorGridLines(width: 0),
        ),
        primaryYAxis: NumericAxis(
          isInversed: false, // Fix inverted Y-axis
          majorGridLines: MajorGridLines(width: 0.5, color: Colors.grey.shade300),
        ),
        plotAreaBorderWidth: 0,
        series: <CartesianSeries>[
          // Bar chart for actual weights
          ColumnSeries<WeightEntry, DateTime>(
            dataSource: actualEntries,
            xValueMapper: (WeightEntry entry, _) => entry.date,
            yValueMapper: (WeightEntry entry, _) => entry.weight,
            color: Color(0xFF09602D).withValues(alpha: 0.7),
            borderColor: Color(0xFF09602D),
            borderWidth: 2,
          ),
          // Blue connecting line for actual weights
          LineSeries<WeightEntry, DateTime>(
            dataSource: actualEntries,
            xValueMapper: (WeightEntry entry, _) => entry.date,
            yValueMapper: (WeightEntry entry, _) => entry.weight,
            color: Colors.blue,
            width: 2,
            markerSettings: MarkerSettings(
              isVisible: true,
              color: Colors.blue,
              borderColor: Colors.white,
              borderWidth: 1,
              height: 6,
              width: 6,
            ),
          ),
          // Red dotted line for expected weights
          LineSeries<WeightEntry, DateTime>(
            dataSource: expectedEntries,
            xValueMapper: (WeightEntry entry, _) => entry.date,
            yValueMapper: (WeightEntry entry, _) => entry.weight,
            color: Colors.red,
            width: 2,
            dashArray: <double>[5, 5], // Dotted line
            markerSettings: MarkerSettings(isVisible: false),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final filteredEntries = _getFilteredEntries();
    // Calculate average daily change (can be negative or positive)
    double avgLoss = 0.0;
    if (widget.entries.length > 1) {
      final sortedEntries = [...widget.entries]
        ..sort((a, b) => a.date.compareTo(b.date));
      double totalChange = 0.0;
      for (int i = 1; i < sortedEntries.length; i++) {
        totalChange += sortedEntries[i].weight - sortedEntries[i - 1].weight;
      }
      int totalDays =
          sortedEntries.last.date.difference(sortedEntries.first.date).inDays;
      if (totalDays > 0) {
        avgLoss = totalChange / totalDays;
      }
    }
    final latestWeight =
        widget.entries.isNotEmpty ? widget.entries.last.weight : 0.0;
    final weekExpected = latestWeight + avgLoss * 7;
    final monthExpected = latestWeight + avgLoss * 30;
    // Prepare day names for the current view
    List<String> dayNames = ['S', 'M', 'T', 'W', 'T', 'F', 'S'];
    // Calculate end of week and month dates
    final now = DateTime.now();
    final endOfWeek = now.add(Duration(days: 7 - (now.weekday % 7)));
    final endOfMonth = DateTime(now.year, now.month + 1, 0);
    String weekDateStr =
        '${dayNames[endOfWeek.weekday % 7]} ${endOfWeek.day}/${endOfWeek.month}';
    String monthDateStr =
        '${dayNames[endOfMonth.weekday % 7]} ${endOfMonth.day}/${endOfMonth.month}';
    String weekSign = avgLoss * 7 > 0 ? '+' : '';
    String monthSign = avgLoss * 30 > 0 ? '+' : '';
    return Column(
      children: [
        // Time period filter tabs in green
        Container(
          margin: EdgeInsets.symmetric(vertical: 8),
          child: TabBar(
            controller: _tabController,
            isScrollable: true,
            labelColor: Color(0xFF09602D), // Green color matching the chart
            unselectedLabelColor: Colors.grey,
            labelStyle: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
            unselectedLabelStyle: TextStyle(
              fontWeight: FontWeight.normal,
              fontSize: 14,
            ),
            indicatorColor: Color(0xFF09602D), // Green indicator
            indicatorWeight: 3,
            tabs: [
              Tab(text: '1 Week'),
              Tab(text: '1 Month'),
              Tab(text: '3 Months'),
              Tab(text: '6 Months'),
              Tab(text: '1 Year'),
            ],
          ),
        ),
        SizedBox(height: 16),
        // Chart based on current view
        _buildChart(filteredEntries, avgLoss),
        // Expected loss/gain text in green, bold
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Column(
            children: [
              Text(
                  'EXPECTED $weekSign${(avgLoss * 7).toStringAsFixed(1)}KG PER WEEK $weekDateStr ${weekExpected.toStringAsFixed(1)}KG',
                  style: TextStyle(
                      color: Color(0xFF09602D),
                      fontWeight: FontWeight.bold,
                      fontSize: 15)),
              Text(
                  'EXPECTED $monthSign${(avgLoss * 30).toStringAsFixed(1)}KG PER MONTH $monthDateStr ${monthExpected.toStringAsFixed(1)}KG',
                  style: TextStyle(
                      color: Color(0xFF09602D),
                      fontWeight: FontWeight.bold,
                      fontSize: 15)),
            ],
          ),
        ),
        // Table with alternating row colors, bold headers
        Padding(
          padding: const EdgeInsets.only(bottom: 55),
          child: _buildStyledTable(filteredEntries),
        ),
      ],
    );
  }

  Widget _buildStyledTable(List<WeightEntry?> entries) {
    // Only show actual entries (not nulls)
    final actualEntries =
        entries.where((e) => e != null).cast<WeightEntry>().toList();
    return Container(
      margin: EdgeInsets.zero,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            height: 33,
            color: Color(0xFF09602D),
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Row(
              children: [
                Expanded(
                    flex: 3,
                    child: Text('DATE',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12))),
                Expanded(
                    flex: 2,
                    child: Text('EXPECTED',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12))),
                Expanded(
                    flex: 2,
                    child: Text('ACTUAL',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12))),
                Expanded(
                    flex: 2,
                    child: Text('DIFF',
                        style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12))),
                SizedBox(width: 40), // Match the delete button width
              ],
            ),
          ),
          ...actualEntries.asMap().entries.map((entry) {
            final index = entry.key;
            final weightEntry = entry.value;
            final previousWeight =
                index > 0 ? actualEntries[index - 1].weight : null;
            final difference = previousWeight != null
                ? weightEntry.weight - previousWeight
                : 0.0;
            final isLoss = difference < 0;
            final avgLoss = actualEntries.length > 1
                ? (actualEntries.first.weight - actualEntries.last.weight) /
                    (actualEntries.length - 1)
                : 0.0;
            final daysSinceFirst =
                weightEntry.date.difference(actualEntries.first.date).inDays;
            final expectedWeight =
                actualEntries.first.weight - avgLoss * daysSinceFirst;
            final rowColor =
                (index + 1) % 2 == 0 ? Color(0xFF09602D) : Color(0xFFFFCC33);
            final textColor =
                (index + 1) % 2 == 0 ? Colors.white : Color(0xFF09602D);
            return Container(
              height: 33,
              color: rowColor,
              padding: EdgeInsets.symmetric(horizontal: 8),
              child: Row(
                children: [
                  Expanded(
                      flex: 3,
                      child: Text(
                          '${_getDayName(weightEntry.date).toUpperCase()} ${weightEntry.date.day}/${weightEntry.date.month}',
                          style: TextStyle(
                              color: textColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12))),
                  Expanded(
                      flex: 2,
                      child: Text('${expectedWeight.toStringAsFixed(1)}',
                          style: TextStyle(
                              color: textColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12))),
                  Expanded(
                      flex: 2,
                      child: Text('${weightEntry.weight.toStringAsFixed(1)}',
                          style: TextStyle(
                              color: textColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12))),
                  Expanded(
                      flex: 2,
                      child: Text('${difference.abs().toStringAsFixed(1)}',
                          style: TextStyle(
                              color: textColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 12))),
                  SizedBox(
                    width: 40,
                    height: 33,
                    child: IconButton(
                      icon: Icon(Icons.delete, color: Colors.red, size: 20),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                      onPressed: () {
                        if (widget.onDelete != null)
                          widget.onDelete!(weightEntry);
                      },
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}

// Helper class for range bar data
class _RangeBarData {
  final String day;
  final double? open;
  final double? close;
  final double? diff;
  final String? diffText; // Text to display the difference
  _RangeBarData({required this.day, this.open, this.close, this.diff, this.diffText});
}

// Helper class for weight difference data
class _WeightDiffData {
  final DateTime date;
  final double difference;
  final bool isIncrease;
  final String label;

  _WeightDiffData({
    required this.date,
    required this.difference,
    required this.isIncrease,
    required this.label,
  });
}
